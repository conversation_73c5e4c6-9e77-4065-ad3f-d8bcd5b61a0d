# Correções no MikrotikExecutor - Ping e Traceroute

## Problema Identificado
- Comandos PING e traceroute não retornavam saída completa
- Complexidade desnecessária do modo interativo (PTY/shell)
- Timeouts e prompts "More:" interrompiam execução
- Lógica excessiva de Ctrl+C e detecção de fim

## Correções Implementadas

### 1. Simplificação do `isInteractiveCommand`
```typescript
// ANTES: ping e traceroute eram tratados como interativos
// DEPOIS: Apenas comandos que realmente precisam de fluxo contínuo
const interactiveCommands = [
  'tool bandwidth-test',
  'tool sniffer', 
  'tool torch'
];
// ping e traceroute removidos da lista
```

### 2. Execução via `ssh.execCommand`
- Comandos ping/traceroute agora usam modo não-interativo
- Eliminação da complexidade do shell PTY
- Melhor estabilidade e performance

### 3. Desativação de Paginação
```typescript
// Executado antes de qualquer comando
await this.ssh.execCommand('terminal length=0', {
  execOptions: { pty: false }
});
```

### 4. Detecção Simplificada de Fim
```typescript
const isPing = command.toLowerCase().includes('ping');
const isTrace = command.toLowerCase().includes('traceroute');

if (
  (isPing  && done.includes('sent=')      && done.includes('received=')) ||
  (isPing  && done.includes('packets transmitted'))                     ||
  (isTrace && done.includes('trace complete'))                          ||
  (isTrace && done.includes('destination reached'))
) {
  // Comando completado
}
```

### 5. Remoção de Lógica Complexa
- ❌ Timeouts específicos (35s ping, 50s traceroute)
- ❌ Envio de Ctrl+C (`\x03`)
- ❌ Detecção complexa de padrões no modo interativo
- ❌ Múltiplos handlers de eventos shell

### 6. Preservação de Formatação
- `cleanAnsiOutput` mantido otimizado
- Remove apenas sequências ANSI
- Preserva colunas de tabelas

## Benefícios

✅ **Saída Completa**: Ping e traceroute retornam resultado completo
✅ **Sem Timeouts**: Eliminação de timeouts prematuros
✅ **Sem Prompts**: Paginação desativada evita "More:"
✅ **Melhor Performance**: Modo não-interativo mais eficiente
✅ **Código Limpo**: Redução significativa de complexidade
✅ **Maior Confiabilidade**: Menos pontos de falha

## Comandos Testáveis

```bash
# Ping com contagem específica
/ping ******* count=4

# Traceroute para destino
/tool traceroute *******

# Ping com interface específica
/ping *********** interface=ether1 count=3
```

## Correções Adicionais - Timeouts e Detecção

### 7. Timeouts Estendidos para Diagnósticos
```typescript
// MikrotikExecutor - Timeouts específicos
if (isPing) {
  commandTimeout = Math.max(commandTimeout, 60000); // 60s para ping
} else if (isTrace) {
  commandTimeout = Math.max(commandTimeout, 120000); // 2min para traceroute
}

// Controller - Timeouts estendidos
if (commandText.toLowerCase().includes('ping')) {
  dynamicTimeout = Math.max(dynamicTimeout, 90000); // 90s
} else if (commandText.toLowerCase().includes('traceroute')) {
  dynamicTimeout = Math.max(dynamicTimeout, 180000); // 3min
}
```

### 8. Detecção Melhorada de Fim
```typescript
// Padrões mais abrangentes para ping
const pingComplete = isPing && (
  (done.includes('sent=') && done.includes('received=')) ||
  done.includes('packets transmitted') ||
  done.includes('packet loss') ||
  done.includes('ping statistics') ||
  done.includes('interrupted')
);

// Padrões mais abrangentes para traceroute
const traceComplete = isTrace && (
  done.includes('trace complete') ||
  done.includes('destination reached') ||
  /\d+\s+\d+\.\d+\.\d+\.\d+.*ms\s*$/m.test(cleanStdout) ||
  /\d+\s+\*\s+\*\s+\*\s*$/m.test(cleanStdout)
);
```

### 9. Logs Detalhados para Debug
- Logs específicos para comandos ping/traceroute
- Análise da saída completa para detecção de padrões
- Logs das últimas linhas quando padrões não são detectados

## Problemas Resolvidos

✅ **Ping com saída incompleta**: Timeout aumentado para 60s, detecção melhorada
✅ **Traceroute com timeout**: Timeout aumentado para 2-3min, padrões regex adicionados
✅ **Timeout global do controller**: Ajustado para comandos de diagnóstico
✅ **Logs melhorados**: Debug detalhado para identificar problemas

## Script de Teste
```bash
# Executar testes das correções
node backend/scripts/test-mikrotik-timeout-fixes.js
```

## Commit Message Atualizada
```
feat: corrigir timeouts e detecção de fim para ping/traceroute no Mikrotik

- Remove ping/traceroute do modo interativo
- Adiciona desativação de paginação automática
- Aumenta timeouts específicos (60s ping, 120s traceroute)
- Melhora detecção de fim com padrões regex
- Ajusta timeout do controller para diagnósticos
- Adiciona logs detalhados para debug
- Preserva formatação de colunas em tabelas
```
