import { NodeSSH } from 'node-ssh';
import { RouterOSAPI } from 'node-routeros-v2';
import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import { BaseExecutor } from './baseExecutor';

/**
 * Executor de comandos para dispositivos Mikrotik
 * Implementação otimizada que prioriza SSH com configurações robustas
 * e fallback para API nativa quando necessário
 */
export class MikrotikExecutor extends BaseExecutor {
  private routerOsApi: RouterOSAPI | null = null;
  private apiConnected: boolean = false;
  private host: string = '';
  private username: string = '';
  private password: string = '';
  private port: number = 8728; // Porta padrão da API Mikrotik
  private connectionAttempts: number = 0;
  private maxConnectionAttempts: number = 2; // Reduzido para 2 tentativas
  private keepaliveInterval: NodeJS.Timeout | null = null;
  private lastCommandTime: number = 0;

  constructor(ssh: NodeSSH) {
    super(ssh);

    // Configurar timeouts otimizados para Mikrotik
    this.BASE_TIMEOUT = 45000; // 45 segundos como base (reduzido)
    this.TIMEOUT_PER_COMMAND = 8000; // 8 segundos adicionais por comando (reduzido)
    this.MAX_TIMEOUT = 120000; // Limite máximo de 2 minutos (reduzido)

    // Extrair informações de conexão do SSH para usar na API
    if (ssh.connection) {
      const config = ssh.connection.config;
      this.host = config.host || '';
      this.username = config.username || '';
      this.password = config.password || '';

      // Inicializar keepalive manual para Mikrotik
      this.startKeepalive();
    }

    // Configurar handler para limpar recursos quando o SSH for encerrado
    ssh.connection?.on('close', () => {
      this.cleanup();
    });

    ssh.connection?.on('error', (error: any) => {
      Logger.error('Erro na conexão SSH Mikrotik:', error);
      this.cleanup();
    });
  }

  /**
   * Inicia o keepalive manual para manter a conexão SSH ativa
   */
  private startKeepalive(): void {
    if (this.keepaliveInterval) {
      clearInterval(this.keepaliveInterval);
    }

    Logger.log('Iniciando keepalive manual para Mikrotik a cada 30 segundos');

    this.keepaliveInterval = setInterval(async () => {
      try {
        // Verificar se passou tempo suficiente desde o último comando
        const timeSinceLastCommand = Date.now() - this.lastCommandTime;

        // Só enviar keepalive se não houve comando recente (últimos 25 segundos)
        if (timeSinceLastCommand > 25000) {
          // Enviar comando simples para manter a conexão ativa
          await this.ssh.execCommand('system identity print', {
            execOptions: { pty: false } // Sem PTY para keepalive
          });
          Logger.log('Keepalive enviado para Mikrotik');
        }
      } catch (error) {
        // Ignorar erros de keepalive silenciosamente
        // Logger.log('Keepalive falhou (normal):', error);
      }
    }, 30000); // A cada 30 segundos
  }

  /**
   * Para o keepalive manual
   */
  private stopKeepalive(): void {
    if (this.keepaliveInterval) {
      clearInterval(this.keepaliveInterval);
      this.keepaliveInterval = null;
      Logger.log('Parando keepalive manual para Mikrotik');
    }
  }

  /**
   * Limpa todos os recursos
   */
  private cleanup(): void {
    this.stopKeepalive();
    this.closeApi();
  }

  /**
   * Fecha a conexão com a API do RouterOS
   */
  public async closeApi(): Promise<void> {
    if (this.routerOsApi && this.apiConnected) {
      try {
        Logger.log(`Fechando conexão API Mikrotik para ${this.host}`);
        await this.routerOsApi.close();
        this.apiConnected = false;
      } catch (error) {
        Logger.error('Erro ao fechar conexão API Mikrotik:', error);
      }
    }
    this.routerOsApi = null;
  }

  /**
   * Inicializa a API do RouterOS (desabilitada por padrão)
   */
  private initializeApi(): void {
    // API desabilitada por padrão para priorizar SSH
    // Pode ser habilitada se necessário
    Logger.log('API Mikrotik desabilitada, usando apenas SSH');
    return;

    /*
    try {
      this.routerOsApi = new RouterOSAPI({
        host: this.host,
        user: this.username,
        password: this.password,
        port: this.port,
        keepalive: true,
        timeout: 20000
      });

      Logger.log(`API Mikrotik inicializada para ${this.host}`);
    } catch (error) {
      Logger.error('Erro ao inicializar API Mikrotik:', error);
      this.routerOsApi = null;
    }
    */
  }

  /**
   * Conecta à API do RouterOS (desabilitada por padrão)
   */
  private async connectApi(): Promise<boolean> {
    // API desabilitada por padrão para priorizar SSH
    return false;

    /*
    if (!this.routerOsApi) {
      this.initializeApi();
      if (!this.routerOsApi) {
        return false;
      }
    }

    try {
      if (this.apiConnected) {
        // Verificar se a conexão ainda está ativa
        try {
          // Teste simples para verificar se a conexão está ativa
          await this.routerOsApi.write('/system/identity/print');
          return true;
        } catch (error) {
          Logger.log('Conexão API Mikrotik perdida, reconectando...');
          this.apiConnected = false;
        }
      }

      this.connectionAttempts++;
      Logger.log(`Conectando à API Mikrotik (${this.host}) - Tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts}`);

      // Criar uma promise com timeout de 3 segundos
      const connectPromise = this.routerOsApi.connect();
      const timeoutPromise = new Promise<void>((_, reject) => {
        setTimeout(() => {
          reject(new Error('API_TIMEOUT'));
        }, 3000); // 3 segundos
      });

      // Executar com timeout
      await Promise.race([connectPromise, timeoutPromise]);

      this.apiConnected = true;
      this.connectionAttempts = 0;

      Logger.log(`Conexão API Mikrotik estabelecida com sucesso: ${this.host}`);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage === 'API_TIMEOUT') {
        Logger.error(`Timeout ao conectar à API Mikrotik (tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts})`);
      } else {
        Logger.error(`Falha ao conectar à API Mikrotik (tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts}):`, error);
      }

      if (this.connectionAttempts >= this.maxConnectionAttempts) {
        this.connectionAttempts = 0;
        Logger.log('Número máximo de tentativas de conexão API Mikrotik excedido, usando SSH como fallback');
        return false;
      }

      return false;
    }
    */
  }

  /**
   * Verifica se um comando é interativo e precisa de PTY
   * @param command Comando a ser verificado
   * @returns true se o comando é interativo
   */
  private isInteractiveCommand(command: string): boolean {
    // Deixe apenas comandos que realmente exigem fluxo contínuo
    const interactiveCommands = [
      'tool bandwidth-test',
      'tool sniffer',
      'tool torch'
    ];

    const cleanCommand = command.toLowerCase().trim();
    return interactiveCommands.some(cmd => cleanCommand.includes(cmd));
  }

  /**
   * Formata comandos de diagnóstico para a sintaxe correta do Mikrotik
   * @param command Comando original
   * @returns Comando formatado
   */
  private formatDiagnosticCommand(command: string): string {
    const cleanCommand = command.trim().toLowerCase();

    // Se o comando já está na sintaxe correta do Mikrotik, retornar como está
    if (command.startsWith('/ping address=') ||
        command.startsWith('/tool traceroute address=') ||
        command.startsWith('/tool ping address=')) {
      return command.trim();
    }

    // CORREÇÃO CRÍTICA: Detectar comandos que já contêm address= mas não começam com /
    if (cleanCommand.startsWith('ping address=') || cleanCommand.startsWith('tool ping address=')) {
      // Apenas adicionar o prefixo / sem duplicar address=
      return `/${command.trim()}`;
    }

    if (cleanCommand.startsWith('tool traceroute address=') || cleanCommand.startsWith('traceroute address=')) {
      // Apenas adicionar o prefixo / sem duplicar address=
      return `/${command.trim()}`;
    }

    // Detectar comandos ping simples (sem address=) e converter para sintaxe Mikrotik
    if (cleanCommand.startsWith('ping ') || cleanCommand === 'ping') {
      const parts = command.trim().split(/\s+/);
      if (parts.length >= 2) {
        const target = parts[1];
        // Usar sintaxe /ping com parâmetros padrão
        return `/ping address=${target} count=4`;
      }
      return '/ping';
    }

    // Detectar comandos traceroute simples (sem address=) e converter para sintaxe Mikrotik
    if (cleanCommand.startsWith('traceroute ') || cleanCommand === 'traceroute') {
      const parts = command.trim().split(/\s+/);
      if (parts.length >= 2) {
        const target = parts[1];
        // Usar sintaxe /tool traceroute
        return `/tool traceroute address=${target}`;
      }
      return '/tool traceroute';
    }

    // Para comandos que já começam com /, manter como estão
    if (command.startsWith('/')) {
      return command.trim();
    }

    // Adicionar / no início para outros comandos
    return `/${command.trim()}`;
  }

  /**
   * Executa comandos de diagnóstico (ping/traceroute) com timeout interno estendido
   * @param cmd Comando sem barra inicial
   * @param isPing Se é comando ping
   * @param isTrace Se é comando traceroute
   * @returns Resultado do comando
   */
  private async executeDiagnosticCommand(
    cmd: string,
    isPing: boolean,
    isTrace: boolean
  ): Promise<CommandResult> {
    // Timeout interno menor que o do controller para garantir que retornemos resultado antes do timeout global
    // Controller tem ~35s, então usamos 25s para ping e 30s para traceroute
    const diagnosticTimeout = isPing ? 25000 : 30000; // 25s para ping, 30s para traceroute

    Logger.log(`Executando comando de diagnóstico ${isPing ? 'PING' : 'TRACEROUTE'} com timeout interno de ${diagnosticTimeout}ms`);

    return new Promise(async (resolve, reject) => {
      let completed = false;
      let timeoutId: NodeJS.Timeout;

      // Timeout interno que resolve antes do controller cancelar
      timeoutId = setTimeout(() => {
        if (!completed) {
          completed = true;
          Logger.log(`Timeout interno atingido para comando ${isPing ? 'PING' : 'TRACEROUTE'} - retornando resultado parcial`);
          resolve({
            stdout: '[TIMEOUT INTERNO] Comando de diagnóstico não completou dentro do tempo limite interno',
            stderr: '',
            code: 124
          });
        }
      }, diagnosticTimeout);

      try {
        // Configurar opções SSH para diagnóstico
        const sshOptions = {
          execOptions: {
            pty: false,
            env: {}
          },
          timeout: diagnosticTimeout + 5000, // Timeout SSH um pouco maior
          onStdout: (chunk: any) => {
            if (chunk.length > 0) {
              Logger.log(`stdout Mikrotik ${isPing ? 'PING' : 'TRACEROUTE'}: ${chunk.length} bytes - "${chunk.toString().substring(0, 100)}${chunk.length > 100 ? '...' : ''}"`);
            }
          },
          onStderr: (chunk: any) => {
            if (chunk.length > 0) {
              Logger.error(`stderr Mikrotik: ${chunk.length} bytes recebidos`);
            }
          }
        };

        // Executar comando
        const result = await this.ssh.execCommand(cmd, sshOptions);

        if (!completed) {
          completed = true;
          clearTimeout(timeoutId);

          // Processar resultado
          const cleanStdout = this.cleanAnsiOutput(result.stdout);
          const cleanStderr = this.cleanAnsiOutput(result.stderr);

          // Análise da saída
          this.analyzeDiagnosticOutput(cleanStdout, isPing, isTrace);

          const formattedOutput = this.formatMikrotikOutput(cleanStdout);

          resolve({
            stdout: formattedOutput,
            stderr: cleanStderr,
            code: result.code || 0
          });
        }
      } catch (error) {
        if (!completed) {
          completed = true;
          clearTimeout(timeoutId);

          const errorMessage = error instanceof Error ? error.message : String(error);
          Logger.error(`Erro no comando de diagnóstico: ${errorMessage}`);

          reject(error);
        }
      }
    });
  }

  /**
   * Analisa a saída de comandos de diagnóstico para verificar se estão completos
   * @param cleanStdout Saída limpa
   * @param isPing Se é comando ping
   * @param isTrace Se é comando traceroute
   */
  private analyzeDiagnosticOutput(cleanStdout: string, isPing: boolean, isTrace: boolean): void {
    const done = cleanStdout.toLowerCase();

    Logger.log(`Analisando saída do comando ${isPing ? 'PING' : 'TRACEROUTE'} para detecção de fim...`);
    Logger.log(`Saída completa (${cleanStdout.length} chars): "${cleanStdout.substring(0, 200)}${cleanStdout.length > 200 ? '...' : ''}"`);

    // Padrões mais abrangentes para detecção de fim
    const pingComplete = isPing && (
      (done.includes('sent=') && done.includes('received=')) ||
      done.includes('packets transmitted') ||
      done.includes('packet loss') ||
      done.includes('ping statistics') ||
      done.includes('interrupted')
    );

    const traceComplete = isTrace && (
      done.includes('trace complete') ||
      done.includes('destination reached') ||
      done.includes('traceroute to') ||
      done.includes('hops max') ||
      done.includes('trace to') ||
      done.includes('no route to host') ||
      done.includes('network unreachable') ||
      done.includes('timeout') ||
      // Padrão específico do Mikrotik: linha final com estatísticas
      /\d+\s+\d+\.\d+\.\d+\.\d+.*ms\s*$/m.test(cleanStdout) ||
      // Padrão de hop final atingido
      /\d+\s+\*\s+\*\s+\*\s*$/m.test(cleanStdout)
    );

    if (pingComplete || traceComplete) {
      Logger.log(`Comando ${isPing ? 'PING' : 'TRACEROUTE'} completado com sucesso - padrão de fim detectado`);
    } else {
      Logger.log(`Comando ${isPing ? 'PING' : 'TRACEROUTE'} pode estar incompleto - padrões de fim não detectados`);
      Logger.log(`Últimas 3 linhas da saída: "${cleanStdout.split('\n').slice(-3).join('\\n')}"`);
    }
  }

  /**
   * Executa um comando em um dispositivo Mikrotik
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      // Atualizar timestamp do último comando
      this.lastCommandTime = Date.now();

      Logger.log('Executando comando Mikrotik:', command);

      // Verificar se o comando contém múltiplas linhas
      if (command.includes('\n')) {
        Logger.log('Detectado comando multilinhas para Mikrotik');
        return await this.executeMultilineCommand(command);
      }

      // Verificar se é um comando interativo
      const isInteractive = this.isInteractiveCommand(command);
      Logger.log(`Comando ${isInteractive ? 'interativo' : 'não-interativo'} detectado: ${command}`);

      if (isInteractive) {
        return await this.executeInteractiveCommand(command);
      }

      // Formatar o comando usando a função específica para diagnósticos
      const formattedCommand = this.formatDiagnosticCommand(command);

      Logger.log(`Executando comando Mikrotik formatado: ${formattedCommand}`);

      // Usar apenas SSH (API desabilitada)
      Logger.log('Usando SSH para executar comando Mikrotik');

      // Desativar paginação antes de executar o comando
      try {
        await this.ssh.execCommand('terminal length=0', {
          execOptions: { pty: false }
        });
        Logger.log('Paginação desativada com sucesso');
      } catch (error) {
        Logger.log('Aviso: Não foi possível desativar paginação:', error);
        // Continuar mesmo se não conseguir desativar paginação
      }

      // Calcular o comando sem a barra inicial para SSH
      const cmd = formattedCommand.startsWith('/') ? formattedCommand.slice(1) : formattedCommand;

      // Detectar comandos de diagnóstico
      const isPing = command.toLowerCase().includes('ping');
      const isTrace = command.toLowerCase().includes('traceroute');

      // Para comandos de diagnóstico, usar timeout interno menor que o do controller
      if (isPing || isTrace) {
        return await this.executeDiagnosticCommand(cmd, isPing, isTrace);
      }

      // Configurar opções SSH otimizadas para comandos normais
      const sshOptions = {
        execOptions: {
          pty: false,  // Desabilitar PTY para melhor estabilidade
          env: {}      // Ambiente limpo
        },
        timeout: this.calculateDynamicTimeout(1),
        onStdout: (chunk: any) => {
          if (chunk.length > 0) {
            Logger.log(`stdout Mikrotik: ${chunk.length} bytes recebidos`);
          }
        },
        onStderr: (chunk: any) => {
          if (chunk.length > 0) {
            Logger.error(`stderr Mikrotik: ${chunk.length} bytes recebidos`);
          }
        }
      };

      // Executar o comando via SSH com retry automático
      let result;
      let retryCount = 0;
      const maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          Logger.log(`Tentativa ${retryCount + 1}/${maxRetries + 1} de execução do comando`);

          result = await this.ssh.execCommand(cmd, sshOptions);

          // Se chegou aqui, comando executado com sucesso
          break;
        } catch (error) {
          retryCount++;
          const errorMessage = error instanceof Error ? error.message : String(error);

          Logger.error(`Erro na tentativa ${retryCount}/${maxRetries + 1}:`, errorMessage);

          // Verificar se é um erro de timeout/keepalive
          if (errorMessage.includes('keepalive') ||
              errorMessage.includes('timeout') ||
              errorMessage.includes('timed out') ||
              errorMessage.includes('connection') ||
              errorMessage.includes('closed')) {

            if (retryCount <= maxRetries) {
              Logger.log(`Aguardando 2 segundos antes da próxima tentativa...`);
              await new Promise(resolve => setTimeout(resolve, 2000));
              continue;
            } else {
              Logger.error('Máximo de tentativas excedido, sinalizando necessidade de reconexão');
              throw new Error('RECONNECT_NEEDED: Falha persistente de conexão SSH');
            }
          } else {
            // Erro não relacionado a conexão, não tentar novamente
            throw error;
          }
        }
      }

      if (!result) {
        throw new Error('Falha ao executar comando após todas as tentativas');
      }

      // Limpar a saída final
      const cleanStdout = this.cleanAnsiOutput(result.stdout);
      const cleanStderr = this.cleanAnsiOutput(result.stderr);

      // Formatar a saída para melhor legibilidade
      const formattedOutput = this.formatMikrotikOutput(cleanStdout);

      Logger.log(`Comando Mikrotik executado com sucesso. Saída: ${formattedOutput.length} caracteres`);

      return {
        stdout: formattedOutput,
        stderr: cleanStderr,
        code: result.code || 0
      };
    } catch (error) {
      Logger.error('Erro ao executar comando Mikrotik:', error);

      // Verificar se é um erro de keepalive timeout
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('RECONNECT_NEEDED') ||
          errorMessage.includes('keepalive') ||
          errorMessage.includes('timeout') ||
          errorMessage.includes('timed out') ||
          errorMessage.includes('connection') ||
          errorMessage.includes('closed')) {
        Logger.log('Detectado erro de conexão, sinalizando necessidade de reconexão');
        throw new Error('RECONNECT_NEEDED: Erro de conexão SSH detectado');
      }

      throw error;
    }
  }

  /**
   * Executa um comando com múltiplas linhas
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  async executeMultilineCommand(command: string): Promise<CommandResult> {
    try {
      // Atualizar timestamp do último comando
      this.lastCommandTime = Date.now();

      // Dividir o comando em linhas individuais
      const lines = command.split('\n').filter(line => line.trim() !== '');
      const commandCount = lines.length;

      // Calcular o timeout dinâmico com base na quantidade de comandos
      const dynamicTimeout = this.calculateDynamicTimeout(commandCount);

      Logger.log(`Executando ${commandCount} comandos Mikrotik separados com timeout dinâmico de ${dynamicTimeout}ms`);

      // Usar apenas SSH (API desabilitada)
      Logger.log('Usando SSH para executar comandos múltiplos Mikrotik');

      // Criar uma promise com timeout global para todos os comandos
      const timeoutPromise = new Promise<CommandResult>((resolve) => {
        setTimeout(() => {
          resolve({
            stdout: '',
            stderr: `[ERRO] Timeout global atingido após ${dynamicTimeout/1000} segundos para ${commandCount} comandos`,
            code: 124 // Código de timeout
          });
        }, dynamicTimeout);
      });

      // Promise para executar todos os comandos
      const executePromise = async (): Promise<CommandResult> => {
        let combinedOutput = '';
        let combinedError = '';
        let lastCode = 0;
        let successfulCommands = 0;

        // Executar cada linha separadamente
        for (let i = 0; i < commandCount; i++) {
          const line = lines[i].trim();
          Logger.log(`Executando linha ${i+1}/${commandCount}: ${line}`);

          // Formatar o comando para SSH
          const cleanCommand = line.replace(/\s+/g, ' ');
          const formattedCommand = cleanCommand.startsWith('/') ? cleanCommand : `/${cleanCommand}`;

          try {
            // Retira a barra inicial
            const cmd = formattedCommand.startsWith('/')
              ? formattedCommand.slice(1)
              : formattedCommand;

            // Configurar opções SSH otimizadas
            const sshOptions = {
              execOptions: {
                pty: false,  // Desabilitar PTY para melhor estabilidade
                env: {}      // Ambiente limpo
              },
              timeout: Math.min(this.calculateDynamicTimeout(1), 30000), // Timeout por comando individual
              onStdout: (chunk: any) => {
                if (chunk.length > 0) {
                  Logger.log(`stdout linha ${i+1}/${commandCount}: ${chunk.length} bytes`);
                }
              },
              onStderr: (chunk: any) => {
                if (chunk.length > 0) {
                  Logger.error(`stderr linha ${i+1}/${commandCount}: ${chunk.length} bytes`);
                }
              }
            };

            // Executar comando com retry limitado
            let result;
            let retryCount = 0;
            const maxRetries = 1; // Apenas 1 retry para comandos múltiplos

            while (retryCount <= maxRetries) {
              try {
                result = await this.ssh.execCommand(cmd, sshOptions);
                break; // Sucesso, sair do loop de retry
              } catch (error) {
                retryCount++;
                const errorMessage = error instanceof Error ? error.message : String(error);

                if (retryCount <= maxRetries &&
                    (errorMessage.includes('timeout') || errorMessage.includes('connection'))) {
                  Logger.log(`Retry ${retryCount}/${maxRetries} para linha ${i+1}`);
                  await new Promise(resolve => setTimeout(resolve, 1000));
                  continue;
                } else {
                  throw error;
                }
              }
            }

            if (result) {
              // Limpar e formatar a saída
              const cleanStdout = this.cleanAnsiOutput(result.stdout);
              const formattedStdout = this.formatMikrotikOutput(cleanStdout);
              combinedOutput += `=== Comando: ${formattedCommand} ===\n${formattedStdout}\n\n`;

              if (result.stderr) {
                const cleanStderr = this.cleanAnsiOutput(result.stderr);
                combinedError += `=== Aviso em: ${formattedCommand} ===\n${cleanStderr}\n\n`;
              }

              lastCode = result.code || 0;
              successfulCommands++;
            }
          } catch (error) {
            Logger.error(`Erro ao executar linha ${i+1}/${commandCount}:`, error);
            const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
            combinedError += `=== Erro em: ${formattedCommand} ===\n${errorMessage}\n\n`;
            lastCode = 1;

            // Verificar se é erro crítico de conexão
            if (errorMessage.includes('keepalive') ||
                errorMessage.includes('connection') ||
                errorMessage.includes('closed')) {
              Logger.error('Erro crítico de conexão detectado, interrompendo execução múltipla');
              combinedError += `\n[ERRO CRÍTICO] Conexão SSH perdida. Comandos restantes cancelados.\n`;
              break;
            }
          }

          // Pequena pausa entre comandos para evitar sobrecarga
          if (i < commandCount - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        Logger.log(`Execução múltipla concluída: ${successfulCommands}/${commandCount} comandos executados com sucesso`);

        return {
          stdout: combinedOutput,
          stderr: combinedError,
          code: lastCode
        };
      };

      // Executar com timeout
      return Promise.race([executePromise(), timeoutPromise]);
    } catch (error) {
      Logger.error('Erro ao executar comandos múltiplos Mikrotik:', error);

      // Verificar se é erro de conexão
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('keepalive') ||
          errorMessage.includes('timeout') ||
          errorMessage.includes('connection') ||
          errorMessage.includes('closed')) {
        throw new Error('RECONNECT_NEEDED: Erro de conexão durante execução múltipla');
      }

      throw error;
    }
  }

  /**
   * Limpa caracteres de controle ANSI da saída do terminal
   * @param output Saída do terminal
   * @returns Saída limpa
   */
  private cleanAnsiOutput(output: string): string {
    try {
      if (!output) return '';

      // Remover apenas sequências ANSI; não faça line.trim().replace(/\s+/g,' ') para preservar colunas de tabelas
      const ansi = /\x1B\[[0-?]*[ -/]*[@-~]/g;
      return output.replace(ansi, '').replace(/\r\n?/g, '\n');
    } catch (error) {
      Logger.error('Erro ao limpar saída ANSI:', error);
      return output; // Retornar a saída original em caso de erro
    }
  }

  /**
   * Formata a saída do Mikrotik para melhor legibilidade
   * @param output Saída limpa do Mikrotik
   * @returns Saída formatada
   */
  private formatMikrotikOutput(output: string): string {
    try {
      if (!output) return '';

      // Dividir em linhas para processamento
      const lines = output.split('\n');

      // Detectar se é uma tabela simples
      const isTable = lines.some(line =>
        line.includes('NAME') &&
        (line.includes('TYPE') || line.includes('MTU') || line.includes('INTERFACE'))
      );

      if (isTable) {
        // Formatar como tabela simples
        return this.formatMikrotikTable(lines);
      }

      // Formatar saída padrão - apenas limpar e organizar
      const formattedLines = lines
        .map(line => line.trim())
        .filter(line => line.length > 0); // Remover linhas vazias

      return formattedLines.join('\n');
    } catch (error) {
      Logger.error('Erro ao formatar saída Mikrotik:', error);
      return output; // Retornar a saída original em caso de erro
    }
  }

  /**
   * Formata uma tabela do Mikrotik de forma simples
   * @param lines Linhas da tabela
   * @returns Tabela formatada
   */
  private formatMikrotikTable(lines: string[]): string {
    try {
      // Encontrar a linha de cabeçalho
      const headerIndex = lines.findIndex(line =>
        line.includes('NAME') &&
        (line.includes('TYPE') || line.includes('MTU') || line.includes('INTERFACE'))
      );

      if (headerIndex === -1) {
        // Não é uma tabela, retornar as linhas originais
        return lines.filter(line => line.trim().length > 0).join('\n');
      }

      // Formatar de forma simples - apenas organizar as linhas
      let formattedOutput = '';

      // Adicionar todas as linhas a partir do cabeçalho
      for (let i = headerIndex; i < lines.length; i++) {
        const line = lines[i].trim();

        // Pular linhas vazias
        if (!line) continue;

        // Adicionar linha com formatação mínima
        formattedOutput += line + '\n';
      }

      return formattedOutput;
    } catch (error) {
      Logger.error('Erro ao formatar tabela Mikrotik:', error);
      return lines.filter(line => line.trim().length > 0).join('\n');
    }
  }

  /**
   * Executa um comando interativo em um dispositivo Mikrotik usando shell
   * @param command Comando interativo a ser executado
   * @returns Resultado do comando
   */
  async executeInteractiveCommand(command: string): Promise<CommandResult> {
    return new Promise(async (resolve, reject) => {
      let shell: any = null;
      let output = '';
      let errorOutput = '';
      let commandCompleted = false;
      let timeoutId: NodeJS.Timeout | null = null;

      try {
        // Atualizar timestamp do último comando
        this.lastCommandTime = Date.now();

        Logger.log(`Executando comando interativo Mikrotik: ${command}`);

        // Formatar o comando usando a função específica para diagnósticos
        const formattedCommand = this.formatDiagnosticCommand(command);

        // Timeout padrão para comandos interativos
        let interactiveTimeout = 90000; // 90 segundos padrão

        // Configurar timeout global
        timeoutId = setTimeout(() => {
          if (!commandCompleted) {
            Logger.log('Timeout atingido para comando interativo Mikrotik');
            commandCompleted = true;
            if (shell) {
              try {
                shell.end();
              } catch (e) {
                // Ignorar erros ao fechar shell
              }
            }
            resolve({
              stdout: output || '[TIMEOUT] Comando interativo não completou dentro do tempo limite',
              stderr: errorOutput,
              code: 124 // Código de timeout
            });
          }
        }, interactiveTimeout);

        // Iniciar shell interativo com PTY habilitado
        shell = await this.ssh.requestShell({
          term: 'vt100',
          rows: 24,
          cols: 120,
          wrap: 120,
          ptyType: 'vanilla'
        });

        // Variáveis de controle
        let commandSent = false;
        let mikrotikReady = false;

        // Configurar handlers para eventos
        shell.on('data', (data: Buffer) => {
          if (commandCompleted) return;

          const chunk = data.toString('utf8');
          output += chunk;

          Logger.log(`Recebido comando interativo (${chunk.length} bytes): ${chunk.substring(0, 200)}${chunk.length > 200 ? '...' : ''}`);

          // Detectar quando o Mikrotik está pronto para receber comandos
          if (!mikrotikReady && (
              chunk.includes('[') && chunk.includes('@') && chunk.includes('] >') ||
              chunk.includes('RouterOS') ||
              chunk.includes('MikroTik'))) {
            Logger.log('Mikrotik pronto detectado, aguardando antes de enviar comando...');
            mikrotikReady = true;

            // Aguardar um pouco e então enviar o comando
            setTimeout(() => {
              if (!commandCompleted && !commandSent) {
                Logger.log(`Enviando comando para Mikrotik: ${formattedCommand}`);
                shell.write(formattedCommand + '\r\n');
                commandSent = true;
              }
            }, 2000); // Aguardar 2 segundos após detectar que está pronto
            return;
          }

          // Verificar se detectamos o prompt "More:" para continuar
          if (chunk.includes('More:') || chunk.includes('[Q quit|SPACE continue|ENTER line]')) {
            shell.write(' ');
            Logger.log('Detectado prompt de paginação, enviando espaço para continuar');
            return;
          }

          // Detectar prompt do Mikrotik para comandos interativos
          // Padrões: [admin@MikroTik] >, [william@CALL CENTER] >, [user@hostname] >
          if ((chunk.includes('[') && chunk.includes('@') && chunk.includes('] >')) ||
              chunk.includes('RouterOS') && chunk.includes('> ') ||
              chunk.includes('MikroTik') && chunk.includes('> ')) {
            Logger.log('Prompt do Mikrotik detectado, comando interativo completado');
            commandCompleted = true;
            if (timeoutId) clearTimeout(timeoutId);
            shell.end();
            resolve({
              stdout: this.cleanAnsiOutput(output),
              stderr: this.cleanAnsiOutput(errorOutput),
              code: 0
            });
            return;
          }
        });

        shell.stderr?.on('data', (data: Buffer) => {
          if (commandCompleted) return;
          const chunk = data.toString('utf8');
          errorOutput += chunk;
          Logger.error(`stderr comando interativo: ${chunk}`);
        });

        shell.on('close', () => {
          if (!commandCompleted) {
            Logger.log('Shell fechado para comando interativo');
            commandCompleted = true;
            if (timeoutId) clearTimeout(timeoutId);
            resolve({
              stdout: this.cleanAnsiOutput(output),
              stderr: this.cleanAnsiOutput(errorOutput),
              code: 0
            });
          }
        });

        shell.on('error', (error: Error) => {
          if (!commandCompleted) {
            Logger.error('Erro no shell interativo:', error);
            commandCompleted = true;
            if (timeoutId) clearTimeout(timeoutId);
            reject(error);
          }
        });

        // Aguardar um pouco para o shell estar pronto
        await new Promise(resolve => setTimeout(resolve, 1000));

        // O comando será enviado automaticamente quando o Mikrotik estiver pronto
        Logger.log(`Aguardando Mikrotik ficar pronto para enviar comando: ${formattedCommand}`);

        // Timeout de segurança caso o Mikrotik não fique pronto
        setTimeout(() => {
          if (!commandSent && !commandCompleted) {
            Logger.log('Timeout de segurança: enviando comando mesmo sem detectar prompt');
            shell.write(formattedCommand + '\r\n');
            commandSent = true;
          }
        }, 10000); // 10 segundos de timeout de segurança

      } catch (error) {
        Logger.error('Erro ao executar comando interativo Mikrotik:', error);
        commandCompleted = true;
        if (timeoutId) clearTimeout(timeoutId);
        if (shell) {
          try {
            shell.end();
          } catch (e) {
            // Ignorar erros ao fechar shell
          }
        }
        reject(error);
      }
    });
  }
}
